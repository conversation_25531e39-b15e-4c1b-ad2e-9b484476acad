using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.IO.Ports;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;


namespace PEMTestSystem.Services.Devices
{
    /// <summary>
    /// 串口通信管理器
    /// 负责串口资源共享、冲突检测、重连机制和通信质量监测
    /// </summary>
    public class SerialPortManager : IDisposable
    {

        private readonly ConcurrentDictionary<string, SerialPortInfo> _serialPorts;
        private readonly ConcurrentDictionary<string, List<string>> _portDeviceMap;
        private readonly Timer _healthCheckTimer;
        private readonly SemaphoreSlim _managementLock;
        private bool _disposed = false;

        public SerialPortManager()
        {
            _serialPorts = new ConcurrentDictionary<string, SerialPortInfo>();
            _portDeviceMap = new ConcurrentDictionary<string, List<string>>();
            _managementLock = new SemaphoreSlim(1, 1);

            // 每30秒检查一次串口健康状态
            _healthCheckTimer = new Timer(PerformHealthCheck, null, TimeSpan.FromSeconds(30), TimeSpan.FromSeconds(30));

            App.AlarmService.Info("串口管理", "串口通信管理器初始化完成");
        }

        /// <summary>
        /// 注册设备到串口
        /// </summary>
        public async Task<bool> RegisterDeviceAsync(string deviceId, string portName, int baudRate, byte slaveAddress)
        {
            await _managementLock.WaitAsync();
            
            try
            {
                // 检查端口是否已存在
                if (!_serialPorts.ContainsKey(portName))
                {
                    var portInfo = new SerialPortInfo
                    {
                        PortName = portName,
                        BaudRate = baudRate,
                        IsOpen = false,
                        LastAccessTime = DateTime.Now,
                        CommunicationQuality = new CommunicationQuality(),
                        DeviceSlaveAddresses = new Dictionary<string, byte>()
                    };

                    _serialPorts[portName] = portInfo;
                    _portDeviceMap[portName] = new List<string>();
                    
                    App.AlarmService.Info("串口管理", $"注册新串口: {portName}, 波特率: {baudRate}");
                }

                // 检查波特率是否匹配
                var existingPort = _serialPorts[portName];
                if (existingPort.BaudRate != baudRate)
                {
                    App.AlarmService.Error("串口管理", $"设备 {deviceId} 的波特率 {baudRate} 与端口 {portName} 现有波特率 {existingPort.BaudRate} 不匹配");
                    return false;
                }

                // 检查从站地址冲突
                foreach (var kvp in existingPort.DeviceSlaveAddresses)
                {
                    if (kvp.Value == slaveAddress && kvp.Key != deviceId)
                    {
                        App.AlarmService.Error("串口管理", $"设备 {deviceId} 的从站地址 {slaveAddress} 与端口 {portName} 上的设备 {kvp.Key} 冲突");
                        return false;
                    }
                }

                // 检查设备是否已注册
                var devicesOnPort = _portDeviceMap[portName];
                if (devicesOnPort.Contains(deviceId))
                {
                    // 检查从站地址是否一致
                    if (existingPort.DeviceSlaveAddresses.TryGetValue(deviceId, out byte existingSlaveAddress))
                    {
                        if (existingSlaveAddress != slaveAddress)
                        {
                            App.AlarmService.Error("串口管理", $"设备 {deviceId} 的从站地址 {slaveAddress} 与已注册的地址 {existingSlaveAddress} 不匹配");
                            return false;
                        }
                    }
                    App.AlarmService.Warning("串口管理", $"设备 {deviceId} 已在端口 {portName} 上注册");
                    return true;
                }

                // 添加设备到端口
                devicesOnPort.Add(deviceId);
                existingPort.DeviceSlaveAddresses[deviceId] = slaveAddress;
                existingPort.DeviceCount = devicesOnPort.Count;

                App.AlarmService.Info("串口管理", $"设备 {deviceId} 注册到端口 {portName}, 从站地址: {slaveAddress}");
                return true;
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("串口管理", $"注册设备 {deviceId} 到端口 {portName} 失败", ex);
                return false;
            }
            finally
            {
                _managementLock.Release();
            }
        }

        /// <summary>
        /// 注销设备
        /// </summary>
        public async Task<bool> UnregisterDeviceAsync(string deviceId, string portName)
        {
            await _managementLock.WaitAsync();
            
            try
            {
                if (_portDeviceMap.TryGetValue(portName, out var devices))
                {
                    devices.Remove(deviceId);

                    if (_serialPorts.TryGetValue(portName, out var portInfo))
                    {
                        // 移除从站地址记录
                        portInfo.DeviceSlaveAddresses.Remove(deviceId);
                        portInfo.DeviceCount = devices.Count;

                        // 如果没有设备使用此端口，关闭并移除
                        if (devices.Count == 0)
                        {
                            if (portInfo.SerialPort?.IsOpen == true)
                            {
                                portInfo.SerialPort.Close();
                            }
                            portInfo.SerialPort?.Dispose();

                            _serialPorts.TryRemove(portName, out _);
                            _portDeviceMap.TryRemove(portName, out _);

                            App.AlarmService.Info("串口管理", $"端口 {portName} 已关闭并移除（无设备使用）");
                        }
                    }
                    
                    App.AlarmService.Info("串口管理", $"设备 {deviceId} 从端口 {portName} 注销");
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("串口管理", $"注销设备 {deviceId} 从端口 {portName} 失败", ex);
                return false;
            }
            finally
            {
                _managementLock.Release();
            }
        }

        /// <summary>
        /// 获取串口实例
        /// </summary>
        public async Task<SerialPort?> GetSerialPortAsync(string portName)
        {
            await _managementLock.WaitAsync();
            
            try
            {
                if (!_serialPorts.TryGetValue(portName, out var portInfo))
                {
                    App.AlarmService.Warning("串口管理", $"端口 {portName} 未注册");
                    return null;
                }

                // 如果串口未创建，创建新的串口实例
                if (portInfo.SerialPort == null)
                {
                    portInfo.SerialPort = new SerialPort
                    {
                        PortName = portName,
                        BaudRate = portInfo.BaudRate,
                        DataBits = 8,
                        Parity = Parity.None,
                        StopBits = StopBits.One,
                        ReadTimeout = 5000,
                        WriteTimeout = 5000
                    };
                }

                // 如果串口未打开，尝试打开
                if (!portInfo.SerialPort.IsOpen)
                {
                    try
                    {
                        portInfo.SerialPort.Open();
                        portInfo.IsOpen = true;
                        portInfo.LastAccessTime = DateTime.Now;
                        portInfo.CommunicationQuality.ConnectionAttempts++;
                        
                        App.AlarmService.Info("串口管理", $"端口 {portName} 打开成功");
                    }
                    catch (Exception ex)
                    {
                        portInfo.CommunicationQuality.FailedConnections++;
                        App.AlarmService.Error("串口管理", $"打开端口 {portName} 失败", ex);
                        return null;
                    }
                }

                portInfo.LastAccessTime = DateTime.Now;
                return portInfo.SerialPort;
            }
            finally
            {
                _managementLock.Release();
            }
        }

        /// <summary>
        /// 记录通信结果
        /// </summary>
        public void RecordCommunicationResult(string portName, bool success, TimeSpan responseTime)
        {
            if (_serialPorts.TryGetValue(portName, out var portInfo))
            {
                var quality = portInfo.CommunicationQuality;
                quality.TotalRequests++;
                
                if (success)
                {
                    quality.SuccessfulRequests++;
                    quality.TotalResponseTime += responseTime;
                    quality.LastSuccessTime = DateTime.Now;
                }
                else
                {
                    quality.FailedRequests++;
                    quality.LastFailureTime = DateTime.Now;
                }

                // 计算成功率
                quality.SuccessRate = (double)quality.SuccessfulRequests / quality.TotalRequests;
                
                // 计算平均响应时间
                if (quality.SuccessfulRequests > 0)
                {
                    quality.AverageResponseTime = quality.TotalResponseTime / quality.SuccessfulRequests;
                }

                portInfo.LastAccessTime = DateTime.Now;
            }
        }

        /// <summary>
        /// 获取端口通信质量
        /// </summary>
        public CommunicationQuality? GetCommunicationQuality(string portName)
        {
            return _serialPorts.TryGetValue(portName, out var portInfo) ? portInfo.CommunicationQuality : null;
        }

        /// <summary>
        /// 获取所有端口状态
        /// </summary>
        public Dictionary<string, SerialPortStatus> GetAllPortStatus()
        {
            var status = new Dictionary<string, SerialPortStatus>();

            foreach (var kvp in _serialPorts)
            {
                var portInfo = kvp.Value;
                status[kvp.Key] = new SerialPortStatus
                {
                    PortName = portInfo.PortName,
                    BaudRate = portInfo.BaudRate,
                    IsOpen = portInfo.IsOpen,
                    DeviceCount = portInfo.DeviceCount,
                    LastAccessTime = portInfo.LastAccessTime,
                    Quality = portInfo.CommunicationQuality,
                    DeviceSlaveAddresses = new Dictionary<string, byte>(portInfo.DeviceSlaveAddresses)
                };
            }

            return status;
        }

        /// <summary>
        /// 获取指定端口上的设备从站地址映射
        /// </summary>
        public Dictionary<string, byte>? GetPortDeviceSlaveAddresses(string portName)
        {
            if (_serialPorts.TryGetValue(portName, out var portInfo))
            {
                return new Dictionary<string, byte>(portInfo.DeviceSlaveAddresses);
            }
            return null;
        }

        /// <summary>
        /// 检查从站地址是否在指定端口上冲突
        /// </summary>
        public bool IsSlaveAddressConflict(string portName, byte slaveAddress, string excludeDeviceId = "")
        {
            if (_serialPorts.TryGetValue(portName, out var portInfo))
            {
                foreach (var kvp in portInfo.DeviceSlaveAddresses)
                {
                    if (kvp.Value == slaveAddress && kvp.Key != excludeDeviceId)
                    {
                        return true;
                    }
                }
            }
            return false;
        }

        /// <summary>
        /// 强制重连端口
        /// </summary>
        public async Task<bool> ReconnectPortAsync(string portName)
        {
            await _managementLock.WaitAsync();

            try
            {
                if (!_serialPorts.TryGetValue(portName, out var portInfo))
                {
                    return false;
                }

                App.AlarmService.Info("串口管理", $"正在重连端口 {portName}");

                // 关闭现有连接
                if (portInfo.SerialPort?.IsOpen == true)
                {
                    portInfo.SerialPort.Close();
                }
                portInfo.SerialPort?.Dispose();
                portInfo.IsOpen = false;

                // 重新创建串口实例
                portInfo.SerialPort = new SerialPort
                {
                    PortName = portName,
                    BaudRate = portInfo.BaudRate,
                    DataBits = 8,
                    Parity = Parity.None,
                    StopBits = StopBits.One,
                    ReadTimeout = 5000,
                    WriteTimeout = 5000
                };

                // 尝试打开
                try
                {
                    portInfo.SerialPort.Open();
                    portInfo.IsOpen = true;
                    portInfo.LastAccessTime = DateTime.Now;
                    portInfo.CommunicationQuality.ConnectionAttempts++;

                    App.AlarmService.Info("串口管理", $"端口 {portName} 重连成功");
                    return true;
                }
                catch (Exception ex)
                {
                    portInfo.CommunicationQuality.FailedConnections++;
                    App.AlarmService.Error("串口管理", $"端口 {portName} 重连失败", ex);
                    return false;
                }
            }
            finally
            {
                _managementLock.Release();
            }
        }

        /// <summary>
        /// 健康检查
        /// </summary>
        private async void PerformHealthCheck(object? state)
        {
            try
            {
                var now = DateTime.Now;
                var portsToCheck = new List<string>();

                // 收集需要检查的端口
                foreach (var kvp in _serialPorts)
                {
                    var portInfo = kvp.Value;

                    // 检查长时间未访问的端口
                    if ((now - portInfo.LastAccessTime).TotalMinutes > 5)
                    {
                        portsToCheck.Add(kvp.Key);
                    }

                    // 检查通信质量
                    var quality = portInfo.CommunicationQuality;
                    if (quality.TotalRequests > 10 && quality.SuccessRate < 0.8)
                    {
                        App.AlarmService.Warning("串口健康", $"端口 {kvp.Key} 通信质量较差，成功率: {quality.SuccessRate:P2}");
                    }
                }

                // 对需要检查的端口进行健康检查
                foreach (var portName in portsToCheck)
                {
                    if (_serialPorts.TryGetValue(portName, out var portInfo))
                    {
                        if (portInfo.SerialPort?.IsOpen == true)
                        {
                            try
                            {
                                // 简单的健康检查：检查串口是否仍然可用
                                var bytesToRead = portInfo.SerialPort.BytesToRead;
                                portInfo.LastAccessTime = now;
                            }
                            catch (Exception ex)
                            {
                                App.AlarmService.Warning("串口健康", $"端口 {portName} 健康检查失败，尝试重连: {ex.Message}");
                                await ReconnectPortAsync(portName);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("串口健康", "健康检查过程中发生异常", ex);
            }
        }

        #region IDisposable 实现

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    try
                    {
                        _healthCheckTimer?.Dispose();

                        // 关闭所有串口
                        foreach (var portInfo in _serialPorts.Values)
                        {
                            if (portInfo.SerialPort?.IsOpen == true)
                            {
                                portInfo.SerialPort.Close();
                            }
                            portInfo.SerialPort?.Dispose();
                        }

                        _serialPorts.Clear();
                        _portDeviceMap.Clear();
                        _managementLock?.Dispose();

                        App.AlarmService.Info("串口管理", "串口通信管理器已释放");
                    }
                    catch (Exception ex)
                    {
                        App.AlarmService.Error("串口管理", "释放串口管理器资源时发生错误", ex);
                    }
                }

                _disposed = true;
            }
        }

        #endregion
    }

    /// <summary>
    /// 串口信息
    /// </summary>
    internal class SerialPortInfo
    {
        public string PortName { get; set; } = string.Empty;
        public int BaudRate { get; set; }
        public SerialPort? SerialPort { get; set; }
        public bool IsOpen { get; set; }
        public int DeviceCount { get; set; }
        public DateTime LastAccessTime { get; set; }
        public CommunicationQuality CommunicationQuality { get; set; } = new();
        public Dictionary<string, byte> DeviceSlaveAddresses { get; set; } = new();
    }

    /// <summary>
    /// 通信质量统计
    /// </summary>
    public class CommunicationQuality
    {
        public long TotalRequests { get; set; }
        public long SuccessfulRequests { get; set; }
        public long FailedRequests { get; set; }
        public double SuccessRate { get; set; }
        public TimeSpan TotalResponseTime { get; set; }
        public TimeSpan AverageResponseTime { get; set; }
        public DateTime LastSuccessTime { get; set; }
        public DateTime LastFailureTime { get; set; }
        public long ConnectionAttempts { get; set; }
        public long FailedConnections { get; set; }
    }

    /// <summary>
    /// 串口状态
    /// </summary>
    public class SerialPortStatus
    {
        public string PortName { get; set; } = string.Empty;
        public int BaudRate { get; set; }
        public bool IsOpen { get; set; }
        public int DeviceCount { get; set; }
        public DateTime LastAccessTime { get; set; }
        public CommunicationQuality Quality { get; set; } = new();
        public Dictionary<string, byte> DeviceSlaveAddresses { get; set; } = new();
    }
}
